package http

import (
	"testing"

	"gitlab.com/backend/api-hrm/domain"
)

func TestEmployeeDataEmailField(t *testing.T) {
	// Test that EmployeeData struct includes email field
	employeeData := domain.EmployeeData{
		Name:    "<PERSON>",
		Email:   "<EMAIL>",
		Phone:   "1234567890",
		Address: "123 Main St",
	}

	// Test ToEmployeeMap includes email
	employeeMap := employeeData.ToEmployeeMap(0)
	
	if employeeMap["email"] != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got %v", employeeMap["email"])
	}

	if employeeMap["name"] != "<PERSON>" {
		t.<PERSON>("Expected name to be '<PERSON>', got %v", employeeMap["name"])
	}

	if employeeMap["phone"] != "1234567890" {
		t.<PERSON><PERSON><PERSON>("Expected phone to be '1234567890', got %v", employeeMap["phone"])
	}

	if employeeMap["address"] != "123 Main St" {
		t.<PERSON><PERSON><PERSON>("Expected address to be '123 Main St', got %v", employeeMap["address"])
	}
}

func TestEmployeeFormStructure(t *testing.T) {
	// Test that the EmployeeForm struct can be created with email field
	// This is a compile-time test to ensure the struct has the email field
	
	// Create a mock form to test the structure
	type TestEmployeeForm struct {
		Name    string `form:"name"`
		Email   string `form:"email"`
		Phone   string `form:"phone"`
		Address string `form:"address"`
	}

	form := TestEmployeeForm{
		Name:    "Jane Doe",
		Email:   "<EMAIL>",
		Phone:   "0987654321",
		Address: "456 Oak Ave",
	}

	if form.Email != "<EMAIL>" {
		t.Errorf("Expected email to be '<EMAIL>', got %v", form.Email)
	}
}
