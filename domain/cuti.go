package domain

// Cuti struct
type Cuti struct {
	TrcID         string `json:"trc_id"`
	HrmEmployeeID string `json:"hrm_employee_id"`
	OutletFkid    string `json:"outlet_fkid"`
	TrcDateStart  string `json:"trc_date_start"`
	TrcDateEnd    string `json:"trc_date_end"`
	TrcReason     string `json:"trc_reason"`
	TrcCreated    string `json:"trc_created"`
	TrcStatus     string `json:"trc_status"`
	Type          string `json:"type"`
	TrcUpdate     string `json:"trc_update"`
}

// CutiDetail struct
type CutiDetail struct {
	NO            int    `json:"NO"`
	TrcID         int    `json:"TRC_ID"`
	OutletFkid    int    `json:"outlet_fkid"`
	Reason        string `json:"REASON"`
	HrmEmployeeID int    `json:"EMPLOYEE_ID"`
	DateStart     string `json:"START_DATE"`
	DateEnd       string `json:"END_DATE"`
	TrcReason     string `json:"DESCR"`
	Status        string `json:"STATUS"`
	TrcCreated    string `json:"CREATED"`
	Nik           string `json:"NIK"`
	EmpName       string `json:"EMPLOYEE_NAME"`
	EmpAddress    string `json:"ADDRESS"`
	EmpPhone      string `json:"PHONE"`
	OtlName       string `json:"OUTLET"`
	Total         int    `json:"TOTAL"`
	SisaCuti      int    `json:"SISA_CUTI"`
	TrcTrcID      int    `json:"ACTION"`
}

// AddCutiRequest struct
type AddCutiRequest struct {
	HrmEmployeeID string `json:"hrm_employee_id"`
	OutletFkid    string `json:"outlet_fkid"`
	TrcDateStart  string `json:"trc_date_start"`
	TrcDateEnd    string `json:"trc_date_end"`
	TrcReason     string `json:"trc_reason"`
	TrcCreated    string `json:"trc_created"`
	TrcStatus     string `json:"trc_status"`
	Type          string `json:"type"`

	Attachments []string `json:"attachments"`
	AdminFkid   int      `json:"admin_fkid"`
	UserType    string   `json:"user_type"`
}

func (c *AddCutiRequest) ToMap() map[string]any {
	return map[string]any{
		"hrm_employee_id": c.HrmEmployeeID,
		"outlet_fkid":     c.OutletFkid,
		"trc_date_start":  c.TrcDateStart,
		"trc_date_end":    c.TrcDateEnd,
		"trc_reason":      c.TrcReason,
		"trc_created":     c.TrcCreated,
		"trc_status":      c.TrcStatus,
		"type":            c.Type,
	}
}

type AddCutiParam struct {
	AddCutiRequest
}

// CutiMobileRequest represents the request structure for mobile cuti submission
type CutiMobileRequest struct {
	HrmEmployeeID string `form:"hrm_employee_id"`
	OutletFkid    string `form:"outlet_fkid"`
	TrcCreated    string `form:"trc_created"`
	DateStart     string `form:"trc_date_start"`
	DateEnd       string `form:"trc_date_end"`
	TrcReason     string `form:"trc_reason"`
	TrcStatus     string `form:"trc_status"`
	Type          string `form:"type"`
	TrcID         string `form:"trc_id"`
}

// DayOffRemains struct
type DayOffRemains struct {
	SisaCuti int `json:"SISA_CUTI"`
}

// DetailsCuti struct
type DetailsCuti struct {
	DtrcID       int    `json:"dtrc_id"`
	TrcID        int    `json:"trc_id"`
	DtrcDate     string `json:"dtrc_date"`
	DateStart    string `json:"date_start"`
	DateEnd      string `json:"date_end"`
	EmployeeFkid int    `json:"employee_fkid"`
	Nik          string `json:"nik"`
	Name         string `json:"name"`
	Address      string `json:"address"`
	Phone        string `json:"phone"`
	Email        string `json:"email"`
	Outlet       string `json:"outlet"`
	DtrcStatus   int    `json:"dtrc_status"`
	Type         int    `json:"type"`
	ReasonCuti   string `json:"reason_cuti"`
	StatusCuti   int    `json:"status_cuti"`
	StatusReason string `json:"status_reason"`
	OutletFkid   int    `json:"outlet_fkid"`
}

// Attachment struct
type Attachment struct {
	AttachID      int    `json:"attach_id"`
	TrcID         int    `json:"trc_id"`
	HrmEmployeeID int    `json:"hrm_employee_id"`
	File          string `json:"file"`
}

// DetailsAttach struct
type DetailsAttach struct {
	DaID         int    `json:"da_id"`
	Status       int    `json:"status"`
	AttachReason string `json:"attach_reason"`
}

// Record struct
type Record struct {
	DtrcFkid     int    `json:"dtrc_fkid"`
	DtrcID       int    `json:"dtrc_id"`
	User         int    `json:"user"`
	Admin        int    `json:"admin"`
	Name         string `json:"name"`
	Email        string `json:"email"`
	DateCuti     string `json:"date_cuti"`
	FromStatus   int    `json:"from_status"`
	ToStatus     int    `json:"to_status"`
	ReasonStatus string `json:"reason_status"`
	DateUpdated  string `json:"date_updated"`
}

// CutiEmployee struct
type CutiEmployee struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

// Admin strcut
type Admin struct {
	AdminID string `json:"admin_id,omitempty"`
	Email   string `json:"email,omitempty"`
	Name    string `json:"name,omitempty"`
}

// EmployeeOutletID struct
type EmployeeOutletID struct {
	OutletFkID int `json:"outlet_fkid"`
}

// EmployeeShift struct
type EmployeeShift struct {
	ShiftID  string `json:"shift_id"`
	OutletID int    `json:"outlet_id"`
}

// ShiftTime struct
type ShiftTime struct {
	ShiftIn        string `json:"shift_in"`
	ShiftOut       string `json:"shift_out"`
	ShiftTolerance int    `json:"shift_tolerance"`
	ShiftType      int    `json:"shift_type"`
	TypeName       string `json:"type_name"`
	TypeHours      int    `json:"type_hours"`
	RestHours      int    `json:"rest_hours"`
}

// EmployeeCutiDetail struct
type EmployeeCutiDetail struct {
	TrcID        int    `json:"trc_id"`
	TrcDateStart string `json:"trc_date_start"`
	TrcDateEnd   string `json:"trc_date_end"`
	TrcReason    string `json:"trc_reason"`
	TrcStatus    int    `json:"trc_status"`
	Type         int    `json:"type"`
}

// EmpRole struct
type EmpRole struct {
	EmpName string `json:"emp_name"`
	Jabatan string `json:"jabatan"`
}

// Attendance struct
type Attendance struct {
	TipJam  string `json:"tip_jam"`
	TipKode string `json:"tip_kode"`
}

// DetailsCutiM struct for fetching cuti details with attachments and records
type DetailsCutiM struct {
	DtrcDate    string     `json:"dtrc_date"`
	DateStart   string     `json:"date_start"`
	DateEnd     string     `json:"date_end"`
	ReasonCuti  string     `json:"reason_cuti"`
	StatusCuti  int        `json:"status_cuti"`
	OutletFkid  int        `json:"outlet_fkid"`
	Attachments []FileCuti `json:"attachments,omitempty"`
	Records     []Record   `json:"records,omitempty"`
}

// OutletLatLing struct
type OutletLatLing struct {
	OutletID   int     `json:"outlet_id"`
	Name       string  `json:"name"`
	Address    string  `json:"address"`
	Latitude   float64 `json:"latitude"`
	Longitude  float64 `json:"longitude"`
	OutletLogo string  `json:"outlet_logo"`
}

type EmployeeDetails struct {
	Name       string `json:"name"`
	Address    string `json:"address"`
	ProfileImg string `json:"profile_img"`
	Phone      string `json:"phone"`
	Photo      string `json:"photo"`
	Email      string `json:"email"`
	Nik        string `json:"nik"`
}

type DeviceToken struct {
	Token string `json:"token"`
}

type FileCuti struct {
	File string `json:"file"`
}

type EmpType struct {
	TypeFkid  int    `json:"type_fkid"`
	TypeName  string `json:"type_name"`
	TypeHours int    `json:"type_hours"`
	RestHours int    `json:"rest_hours"`
}

type HrmEmpID struct {
	HrmEmployeeID  int    `json:"hrm_employee_id"`
	AdminFkid      int    `json:"admin_fkid"`
	DataCreated    int    `json:"data_created"`
	DataModified   int    `json:"data_modified"`
	EmployeeFkid   int    `json:"employee_fkid"`
	DataStatus     string `json:"data_status"`
	Nik            string `json:"nik"`
	TypeFkid       int    `json:"type_fkid"`
	EmployeeSalary int    `json:"employee_salary"`
	ProfileImg     string `json:"profile_img"`
}

// SaveDetailCutiRequest struct represents the data structure for saving cuti details
type SaveDetailCutiRequest struct {
	Status     []map[string]any `json:"status" form:"status"`             // Status information
	Date       []map[string]any `json:"_date" form:"_date"`               // Date information
	Documents  []string         `json:"doc" form:"doc"`                   // Document attachments
	Keys       []map[string]any `json:"key" form:"key"`                   // Key information
	Reasons    []map[string]any `json:"reason" form:"reason"`             // Reason information
	StatusInfo []map[string]any `json:"_status" form:"_status"`           // Status information
	Nik        string           `json:"nik" form:"nik"`                   // NIK
	TrcID      string           `json:"trc_id" form:"inputTransCutiId"`   // Transaction ID
	EmpID      string           `json:"emp_id" form:"emp_id"`             // Employee ID
	OutletID   string           `json:"outlet_id" form:"outlet_id"`       // Outlet ID
	EmpName    string           `json:"emp_name" form:"emp_name"`         // Employee name
	Email      string           `json:"email" form:"email"`               // Email
	Type       string           `json:"type" form:"inputTypeAdm"`         // Type
	Admin      string           `json:"admin" form:"inputAdmin"`          // Admin
	User       string           `json:"user" form:"inputUserID"`          // User
	EmailAdmin string           `json:"email_admin" form:"inputEmailAdm"` // Admin email
	Idx        []string
}

// CutiV2Detail struct for v2 API
type CutiV2Detail struct {
	NO           int    `json:"no"`
	TrcID        int    `json:"trc_id"`
	OutletFkid   int    `json:"outlet_fkid"`
	EmployeeID   int    `json:"employee_id"`
	StartDate    string `json:"start_date"`
	EndDate      string `json:"end_date"`
	Descr        string `json:"descr"`
	Created      string `json:"created"`
	Nik          string `json:"nik"`
	EmployeeName string `json:"employee_name"`
	Address      string `json:"address"`
	Phone        string `json:"phone"`
	Outlet       string `json:"outlet"`
	Reason       string `json:"reason"`
	Status       string `json:"status"`
	Action       int    `json:"action"`
}

// TimeOffConflict struct to represent conflicting time off requests
type TimeOffConflict struct {
	TrcID      int    `json:"trc_id"`
	StartDate  string `json:"start_date"`
	EndDate    string `json:"end_date"`
	Status     string `json:"status"`
	StatusText string `json:"status_text"`
	Reason     string `json:"reason"`
}

// CutiFilter represents the filter parameters for fetching cuti records
type CutiFilter struct {
	OutletIDs string `json:"outlet_ids"` // Comma-separated list of outlet IDs
	Status    string `json:"status"`     // Comma-separated list of statuses (pending,approved,rejected)
	StartDate string `json:"start_date"` // Start date in DD-MM-YYYY format
	EndDate   string `json:"end_date"`   // End date in DD-MM-YYYY format
}

// CutiContract interface
type CutiContract interface {
	Fetch(adminID int) ([]CutiDetail, error)
	FetchDayOffRemains(empID int) (DayOffRemains, error)
	FetchCutiV2(adminID int, filter CutiFilter) ([]CutiV2Detail, error)

	AddCuti(cuti map[string]any) (int64, error)
	AddAttch(attachment []map[string]any) error
	AddDetailTransCuti(details map[string]any) (int64, error)
	AddRecordCuti(record map[string]any) error
	AddDetailAttch(dtAttach map[string]any) (int64, error)

	FetchDetailsCuti(trcID, adminID int) ([]DetailsCuti, error)
	FetchAttach(trcID int) ([]Attachment, error)
	FetchDetailsAttch(trcID int) ([]DetailsAttach, error)
	FetchRecord(trcID int) ([]Record, error)
	FetchEmployee() ([]CutiEmployee, error)
	FetchAdmin() ([]Admin, error)

	SaveDetailCuti(cuti SaveDetailCutiRequest) error

	CancelCuti(data map[string]any) error

	DeleteCuti(data map[string]any) error

	// api v2
	FetchEmployeeOutlet(empID int) ([]EmployeeOutletID, error)
	FetchShift(outletID int, shiftCode string) ([]ShiftTime, error)
	FetchEmployeeShift(empID int, outletID int, date string) ([]EmployeeShift, error)
	FetchEmployeeCutiDetail(empID int) ([]EmployeeCutiDetail, error)
	FetchSingleEmployeeCuti(trcID int) (EmployeeCutiDetail, error)
	FetchEmpRole(empID int) ([]EmpRole, error)
	FetchAttendance(outletID int, date string, empID int) ([]Attendance, error)
	Absensi(data map[string]any) error
	FetchCutiDetails(trcID int) ([]DetailsCutiM, error)
	FetchOutletLatLing(outletID []string) ([]OutletLatLing, error)
	FetchEmployeeDetails(employeeID int) ([]EmployeeDetails, error)
	FetchHrmEmpID(employeeID int) (HrmEmpID, error)
	GetUserDeviceToken(empID int) ([]DeviceToken, error)
	SaveMessage(data map[string]any) (int64, error)
	FetchFile(trcID int) ([]FileCuti, error)
	FetchEmpType(empID int) ([]EmpType, error)
}

// CutiUseCase interface
type CutiUseCase interface {
	CutiContract
	AddCutiV2(cuti AddCutiRequest) (int64, error)
	FetchCompleteDetails(cutiID, businessID int) (map[string]interface{}, error)
}

// CutiRepository interface
type CutiRepository interface {
	CutiContract
	AddCutiV2(param AddCutiParam) error
	// New method for checking existing time off
	CheckExistingTimeOff(employeeID string, startDate, endDate string) ([]TimeOffConflict, error)
}
